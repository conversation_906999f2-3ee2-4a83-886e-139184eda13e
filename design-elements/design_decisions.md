# Design Decisions

## Core Architecture

### Unified Recursive Streaming Architecture
- **Implementation**: Single recursive function `insert_json_value_recursive()` handles all JSON value types uniformly
- **Critical Success Factor**: Avoids special-case handling and enables arbitrary nesting depth without hardcoded limits
- **Vector Routing**: Routes to appropriate vector insertion logic based on value type and expected schema

### Pure Struson Streaming
- **Decision**: Complete elimination of serde_json intermediate representations
- **Implementation**: Direct streaming from JSON to DuckDB vectors using only struson parser
- **Memory Efficiency**: Achieves O(row_size) instead of O(file_size) memory usage
- **Temporary Structures**: Use `TempValue` and `NestedTempValue` only for collection during streaming, not full parsing

### DuckDB Vector Management Patterns
- **Arrays of Objects**: Use `list_vector.struct_child(capacity)`
- **Struct Fields**: Use `struct_vector.child(field_idx, capacity)`
- **Multi-dimensional Arrays**: Recursive list vector management with proper capacity allocation
- **Critical**: Each array element must get its own memory location to avoid data duplication

### Schema Discovery Strategy
- **Implementation**: `discover_json_schema()` with recursive type analysis of first element
- **Rationale**: Sufficient for homogeneous data, enables schema-driven processing for type safety
- **Scope**: Handles arbitrary nesting depth without hardcoded limits

### Schema Validation: Strict vs Permissive Approach
- **Current Decision**: Strict schema validation - reject inconsistent data with clear error messages
- **Example**: Mixed-depth arrays like `[1, [2, 3], [[4, 5]]]` are rejected as schema conflicts
- **Rationale**:
  - Ensures data quality and type safety
  - Provides clear feedback about data structure issues
  - Prevents undefined behavior from inconsistent schemas
- **DuckDB Default Comparison**: DuckDB's default JSON reader is more permissive:
  - Converts mixed types to `JSON[]` with string representations
  - Example: `[1, [2, 3], [[4, 5]]]` → `[('1',), ('[2,3]',), ('[[4,5]]',)]`
- **Future Consideration**: May add permissive mode in future versions for better compatibility
- **Trade-offs**:
  - **Strict (Current)**: Better type safety, clearer errors, forces data quality
  - **Permissive (DuckDB)**: Better compatibility, handles edge cases, may hide data issues

## Critical Anti-Patterns to Avoid

### Never Manual Nesting Logic
- **Anti-Pattern**: Manually implementing N levels of nested logic (if depth == 1, if depth == 2, etc.)
- **Correct Approach**: Use recursive helper functions that handle arbitrary depth

### No Massive Functions with Duplicated Patterns
- **Anti-Pattern**: Single large function handling all cases with repeated code patterns
- **Correct Approach**: Break into focused helper functions with clear responsibilities

### Depth Limits are Anti-Patterns
- **Anti-Pattern**: Hardcoded depth limits (5, 10, 20 levels)
- **Correct Approach**: True recursive processing without artificial constraints

### Null Placeholders for Valid Data
- **Anti-Pattern**: Setting valid JSON structures to null when processing is complex
- **Correct Approach**: Preserve all valid JSON structures with complete data

### Schema-Driven Processing
- Use discovered schema to guide vector allocation and type handling
- Ensures type safety and proper memory management

## Type Mapping
- JSON String → DuckDB VARCHAR
- JSON Number → DuckDB DOUBLE
- JSON Boolean → DuckDB BOOLEAN
- JSON Object → DuckDB STRUCT
- JSON Array → DuckDB LIST
- JSON null → DuckDB NULL

## API Design

### Table Function Interface
```sql
SELECT * FROM streaming_json_reader('path/to/file.json')
```

### Root-Level Array Flattening
- Arrays at root level are flattened into separate rows
- `[{"id": 1}, {"id": 2}]` becomes two rows with `id` column
- `[[1,2], [3,4]]` becomes two rows of `DOUBLE[]`
- Matches DuckDB's `read_json_auto()` behavior for compatibility

### Error Handling
- Fail-fast on malformed JSON rather than attempting partial recovery
- Clear failure modes for debugging

## Implementation Status

### Schema Discovery
In this version, scan the full file to get the exact schema.
Finding the schema doesn't require loading any values into memory so it complies with low memory usage.

### API Surface
Single parameter API: `streaming_json_reader(file_path)` with query-driven behavior.
- `SELECT *` should match DuckDB's `read_json_auto()` behavior in many cases, but can be simplified in cases like an empty object `{}` or when there are schema inconsistencies
- Context-preserving flattening via query structure
- Query planner integration determines behavior


### Critical Architectural Lessons Learned

#### Anti-Pattern: Manual Depth Handling (NEVER DO THIS)

**Problem Identified**: The original implementation contained a massive 259-line `insert_structured_value_with_depth()` function that manually handled 4 levels of nesting with hardcoded logic.

**Why This Was Wrong**:
- **Code Duplication**: Each nesting level repeated the same pattern matching logic
- **Arbitrary Limitations**: Only handled exactly 4 levels, failing on deeper valid JSON
- **Maintenance Nightmare**: Changes required updating multiple identical code blocks
- **Performance Overhead**: Unnecessary intermediate processing at each manual level
- **Architectural Violation**: Contradicted the principle of handling arbitrary valid JSON

**Correct Pattern**: Use proper recursion with helper functions like `insert_struct_recursive()` that handle arbitrary depth cleanly.

#### Anti-Pattern: Arbitrary Depth Limits (FORBIDDEN)

**Problem Identified**: Four different arbitrary depth limits were found:
- Depth > 15: "Maximum nesting depth exceeded"
- Depth > 2: "Deep nesting detected"
- Depth > 20: "Maximum nesting depth exceeded"
- Depth > 5: "Deep nesting detected"

**Why Depth Limits Are Fundamentally Flawed**:
- **No Technical Justification**: Numbers were arbitrary, not based on actual constraints
- **User Experience Violation**: Valid JSON should not fail due to artificial limits
- **Type System Violation**: Forced conversion of STRUCT data to VARCHAR strings
- **Inconsistency**: Four different limits indicated no coherent design
- **Memory Efficiency Violation**: Real JSON can legitimately have deep nesting

**Correct Pattern**: Use proper recursive algorithms that handle depth naturally through stack management, not artificial cutoffs.

#### Anti-Pattern: VARCHAR Fallbacks for STRUCT Data (STRICTLY FORBIDDEN)

**Problem Identified**: Five code blocks converted STRUCT data to VARCHAR strings when encountering "difficult" scenarios.

**Why This Violates Core Design Principles**:
- **Type System Integrity**: DuckDB's STRUCT types provide structured access that VARCHAR cannot
- **Query Capability Loss**: Converting to VARCHAR eliminates the ability to access nested fields
- **Performance Degradation**: String parsing is slower than direct STRUCT field access
- **Data Model Violation**: JSON objects should map to STRUCT types, not strings
- **Regression to Barbarism**: Defeats the entire purpose of structured JSON handling

**Correct Pattern**: Always use proper STRUCT types with recursive vector handling, setting NULL only for genuinely invalid data.

#### Correct Pattern: Recursive Helper Functions

**Why "For Now" Is Forbidden in Memory-Critical Systems**:
- **Memory Corruption**: Approximations in memory offsets cause data overwrites and crashes
- **Exponential Debugging Cost**: "For now" solutions become exponentially harder to fix over time
- **Architectural Debt**: Creates incorrect patterns that infect the entire codebase
- **Root Cause Indicator**: "For now" indicates insufficient understanding of DuckDB's exact memory layout requirements

**Correct Response When Tempted to Write "For Now"**:
1. **Stop Immediately**: Do not implement the approximation
2. **Research**: Study DuckDB API patterns in design-elements/ documentation
3. **Understand**: Learn the exact memory layout requirements before proceeding
4. **Implement Properly**: Use exact cumulative offsets based on actual data sizes, not estimates

**Evidence of Damage**: Multi-row list vector processing with estimated offsets caused systematic data corruption that took significant debugging effort to identify and fix properly.

## Handling duckdb functions

Duckdb's unnest function must work and be tested.
Duckdb's count and sum functions must work and be tested.
- If duckdb has a way to optimize function calls like this by processing in batches, we should enable that.
Duckdb's group by function must work and be tested.
Duckdb's order by function must work and be tested.
Duckdb's where function must work and be tested.
Duckdb's join function must work and be tested.
Duckdb's having function must work and be tested.
Duckdb's limit function must work and be tested.
Duckdb's offset function must work and be tested.
